#!/usr/bin/env python3
"""
Test script to verify all audio artifact fixes are working.
This tests the complete audio pipeline from API to browser.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the orpheus directory to the path
sys.path.insert(0, str(Path(__file__).parent / "orpheus"))

from orpheus.tts import OrpheusTTS, Voice

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_all_fixes():
    """Test all the audio artifact fixes"""
    
    # Check if API key is available
    api_key = os.getenv("ORPHEUS_API_KEY")
    if not api_key:
        print("❌ ORPHEUS_API_KEY not set, cannot test")
        return
    
    print("🔧 Testing All Audio Artifact Fixes")
    print("=" * 50)
    
    # Test text
    test_text = "Hello, this is a comprehensive test of all audio artifact fixes."
    
    print(f"Test text: '{test_text}'")
    print()
    
    # Test 1: Non-streaming mode (should have NO artifacts now)
    print("📦 Test 1: Non-Streaming Mode (No Chunking)")
    print("-" * 40)
    try:
        non_streaming_voice = Voice(id="tara_async", name="<PERSON>")
        non_streaming_tts = OrpheusTTS(
            voice=non_streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = non_streaming_tts.synthesize(test_text)
        
        frame_count = 0
        total_duration = 0.0
        
        async for audio_event in synthesis_stream:
            frame_count += 1
            total_duration += audio_event.frame.duration
            
            if frame_count >= 3:  # Test first few frames
                break
        
        print(f"  ✅ Non-streaming: {frame_count} frames, {total_duration:.2f}s duration")
        print("  🎯 Expected: Single large frame, no chunking artifacts")
        
        await non_streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ Non-streaming test failed: {e}")
    
    print()
    
    # Test 2: Streaming mode (should have reduced artifacts)
    print("🔄 Test 2: Streaming Mode (Improved Chunking)")
    print("-" * 40)
    try:
        streaming_voice = Voice(id="tara", name="Tara")
        streaming_tts = OrpheusTTS(
            voice=streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = streaming_tts.synthesize(test_text)
        
        frame_count = 0
        total_duration = 0.0
        
        async for audio_event in synthesis_stream:
            frame_count += 1
            total_duration += audio_event.frame.duration
            
            if frame_count >= 5:  # Test more frames for streaming
                break
        
        print(f"  ✅ Streaming: {frame_count} frames, {total_duration:.2f}s duration")
        print("  🎯 Expected: Multiple frames with improved processing")
        
        await streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ Streaming test failed: {e}")
    
    print()
    
    # Summary of fixes applied
    print("🎯 Summary of Fixes Applied")
    print("=" * 30)
    print("✅ Frontend AudioContext: 44100 Hz → 24000 Hz (matches LiveKit)")
    print("✅ TTS Service: Removed volume=0.98 filter")
    print("✅ TTS Service: Removed fade in/out processing")
    print("✅ TTS Service: Removed silence padding")
    print("✅ Orpheus Plugin: Separated streaming vs non-streaming processing")
    print("✅ Orpheus Plugin: Improved frame sizes (10ms → 20ms)")
    print("✅ Orpheus Plugin: Larger chunk sizes (1KB → 4KB)")
    print("✅ Orpheus Plugin: Better buffer management")
    print()
    
    print("🎵 Expected Results:")
    print("   - Non-streaming mode should have ZERO 'tuk tuk' artifacts")
    print("   - Streaming mode should have significantly reduced artifacts")
    print("   - Overall audio quality should be much cleaner")
    print("   - No sample rate conversion artifacts")
    print("   - No volume processing artifacts")
    print()
    
    print("🧪 Next Steps:")
    print("   1. Test in your web application")
    print("   2. Compare audio quality before/after fixes")
    print("   3. If artifacts persist, they're likely from:")
    print("      - Browser audio processing")
    print("      - WebRTC codec issues")
    print("      - Network/connection problems")

async def main():
    """Run the comprehensive test"""
    await test_all_fixes()
    
    print()
    print("✅ Comprehensive audio artifact fix test completed!")

if __name__ == "__main__":
    asyncio.run(main())
