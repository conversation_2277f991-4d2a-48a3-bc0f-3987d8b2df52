#!/usr/bin/env python3
"""
Test script to verify non-streaming mode works without artifacts.
This script tests the difference between streaming and non-streaming audio processing.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the orpheus directory to the path
sys.path.insert(0, str(Path(__file__).parent / "orpheus"))

from orpheus.tts import OrpheusTTS, Voice
from orpheus.models import AVAILABLE_VOICES

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_streaming_vs_non_streaming():
    """Test both streaming and non-streaming modes"""
    
    # Check if API key is available
    api_key = os.getenv("ORPHEUS_API_KEY")
    if not api_key:
        print("❌ ORPHEUS_API_KEY not set, cannot test")
        return
    
    test_text = "Hello, this is a test to compare streaming versus non-streaming audio processing."
    
    print("🧪 Testing Streaming vs Non-Streaming Audio Processing")
    print("=" * 60)
    print(f"Test text: '{test_text}'")
    print()
    
    # Test streaming mode
    print("🔄 Testing STREAMING mode (tara)...")
    try:
        streaming_voice = Voice(id="tara", name="Tara")  # Streaming voice
        streaming_tts = OrpheusTTS(
            voice=streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = streaming_tts.synthesize(test_text)
        
        frame_count = 0
        total_duration = 0.0
        
        async for audio_event in synthesis_stream:
            frame_count += 1
            total_duration += audio_event.frame.duration
            
            # Only process first few frames for testing
            if frame_count >= 3:
                break
        
        print(f"  ✅ STREAMING: Generated {frame_count} frames")
        print(f"  ✅ STREAMING: Total duration: {total_duration:.2f} seconds")
        
        await streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ STREAMING test failed: {e}")
    
    print()
    
    # Test non-streaming mode
    print("📦 Testing NON-STREAMING mode (tara_async)...")
    try:
        non_streaming_voice = Voice(id="tara_async", name="Tara")  # Non-streaming voice
        non_streaming_tts = OrpheusTTS(
            voice=non_streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = non_streaming_tts.synthesize(test_text)
        
        frame_count = 0
        total_duration = 0.0
        
        async for audio_event in synthesis_stream:
            frame_count += 1
            total_duration += audio_event.frame.duration
            
            # Only process first few frames for testing
            if frame_count >= 3:
                break
        
        print(f"  ✅ NON-STREAMING: Generated {frame_count} frames")
        print(f"  ✅ NON-STREAMING: Total duration: {total_duration:.2f} seconds")
        
        await non_streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ NON-STREAMING test failed: {e}")
    
    print()
    print("🎯 Expected Results:")
    print("   - STREAMING: Multiple small frames with chunked processing")
    print("   - NON-STREAMING: Single large frame with complete audio")
    print("   - NON-STREAMING should have NO artifacts since no chunking occurs")
    print()

def print_voice_info():
    """Print information about available voices"""
    print("🎤 Available Voices")
    print("=" * 30)
    
    for voice_data in AVAILABLE_VOICES:
        mode = "STREAMING" if voice_data["is_streaming"] else "NON-STREAMING"
        print(f"  {voice_data['name']} ({voice_data['id']}) - {mode}")
        print(f"    API Name: {voice_data['api_voice_name']}")
    print()

async def main():
    """Run the test"""
    print("🔧 Non-Streaming Audio Test")
    print("=" * 40)
    print()
    
    print_voice_info()
    await test_streaming_vs_non_streaming()
    
    print("✅ Test completed!")
    print()
    print("💡 Key Insight:")
    print("   If non-streaming mode still has artifacts, the issue is NOT in")
    print("   the chunking/streaming logic, but in:")
    print("   1. The audio data from the API itself")
    print("   2. LiveKit's audio frame handling")
    print("   3. The web calling interface")
    print("   4. Browser audio processing")

if __name__ == "__main__":
    asyncio.run(main())
