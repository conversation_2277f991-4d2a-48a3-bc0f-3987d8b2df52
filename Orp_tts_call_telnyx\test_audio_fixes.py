#!/usr/bin/env python3
"""
Test script to verify audio artifact fixes in the Orpheus TTS plugin.
This script tests the improved audio processing pipeline.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the orpheus directory to the path
sys.path.insert(0, str(Path(__file__).parent / "orpheus"))

from orpheus.tts import OrpheusTTS, Voice, calculate_text_threshold_from_bytes
from orpheus.models import AVAILABLE_VOICES

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_chunk_size_calculations():
    """Test the improved chunk size calculations"""
    print("🧪 Testing Chunk Size Calculations")
    print("=" * 50)
    
    test_sizes = [1024, 2048, 4096, 8192, 16384]
    for size in test_sizes:
        threshold = calculate_text_threshold_from_bytes(size)
        preset = "Conservative" if size <= 1024 else "Balanced" if size <= 4096 else "Aggressive"
        print(f"  {size:5d} bytes → {threshold:3d} characters ({preset})")
    print()

def test_voice_configurations():
    """Test voice configurations"""
    print("🎤 Testing Voice Configurations")
    print("=" * 50)
    
    for voice_data in AVAILABLE_VOICES:
        voice = Voice(id=voice_data["id"], name=voice_data["name"])
        print(f"  Voice: {voice.name} ({voice.id})")
        print(f"    Streaming: {voice_data['is_streaming']}")
        print(f"    API Name: {voice_data['api_voice_name']}")
    print()

async def test_tts_initialization():
    """Test TTS initialization with different configurations"""
    print("🔧 Testing TTS Initialization")
    print("=" * 50)
    
    # Test different chunk sizes
    chunk_sizes = [1024, 4096, 8192]
    
    for chunk_size in chunk_sizes:
        try:
            voice = Voice(id="tara", name="Tara")
            tts = OrpheusTTS(
                voice=voice,
                chunk_size_bytes=chunk_size
            )
            
            print(f"  ✅ TTS initialized with {chunk_size} byte chunks")
            print(f"     Text threshold: {calculate_text_threshold_from_bytes(chunk_size)} characters")
            
            # Clean up
            await tts.aclose()
            
        except Exception as e:
            print(f"  ❌ Failed to initialize TTS with {chunk_size} bytes: {e}")
    
    print()

def test_audio_frame_settings():
    """Test audio frame settings"""
    print("🎵 Testing Audio Frame Settings")
    print("=" * 50)
    
    # Test frame size calculations
    sample_rate = 24000
    frame_sizes_ms = [10, 20, 30]
    
    for frame_ms in frame_sizes_ms:
        samples_per_channel = sample_rate // (1000 // frame_ms)
        bytes_per_frame = samples_per_channel * 2  # 16-bit audio
        
        print(f"  {frame_ms:2d}ms frames: {samples_per_channel:4d} samples, {bytes_per_frame:4d} bytes")
    
    print()
    print("  ✅ Using 20ms frames (480 samples, 960 bytes) - VoIP standard")
    print()

async def test_synthesis_pipeline():
    """Test the synthesis pipeline with a short text"""
    print("🚀 Testing Synthesis Pipeline")
    print("=" * 50)
    
    # Only test if API key is available
    api_key = os.getenv("ORPHEUS_API_KEY")
    if not api_key:
        print("  ⚠️ ORPHEUS_API_KEY not set, skipping synthesis test")
        return
    
    try:
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(
            voice=voice,
            chunk_size_bytes=4096,  # Use balanced setting
            api_key=api_key
        )
        
        test_text = "Hello, this is a test of the improved audio processing pipeline."
        print(f"  Testing with text: '{test_text}'")
        
        # Create synthesis stream
        synthesis_stream = tts.synthesize(test_text)
        
        frame_count = 0
        total_duration = 0.0
        
        async for audio_event in synthesis_stream:
            frame_count += 1
            total_duration += audio_event.frame.duration
            
            # Only process first few frames for testing
            if frame_count >= 5:
                break
        
        print(f"  ✅ Generated {frame_count} frames")
        print(f"  ✅ Total duration: {total_duration:.2f} seconds")
        
        await tts.aclose()
        
    except Exception as e:
        print(f"  ❌ Synthesis test failed: {e}")
    
    print()

def print_summary():
    """Print summary of improvements"""
    print("📋 Summary of Audio Artifact Fixes")
    print("=" * 50)
    print("✅ Frame size increased from 10ms to 20ms (VoIP standard)")
    print("✅ Chunk size increased from 1KB to 4KB for smoother processing")
    print("✅ Improved audio buffering with minimum buffer size")
    print("✅ Better WAV header removal logic")
    print("✅ Proper AudioByteStream usage for frame management")
    print("✅ Reduced silence padding from 50ms to 20ms")
    print("✅ Optimized text thresholds for better responsiveness")
    print("✅ Consistent 24kHz sample rate throughout pipeline")
    print()
    print("🎯 Expected Results:")
    print("   - Reduced 'tuk tuk' artifacts from small frame processing")
    print("   - Smoother audio transitions between chunks")
    print("   - Less audio cutting at start/end of speech")
    print("   - Better overall audio quality and continuity")
    print()

async def main():
    """Run all tests"""
    print("🔧 Audio Artifact Fix Testing Suite")
    print("=" * 60)
    print()
    
    # Run tests
    test_chunk_size_calculations()
    test_voice_configurations()
    await test_tts_initialization()
    test_audio_frame_settings()
    await test_synthesis_pipeline()
    print_summary()
    
    print("✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
