#!/usr/bin/env python3
"""
Test script to verify the timeout fix works correctly.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the orpheus directory to the path
sys.path.insert(0, str(Path(__file__).parent / "orpheus"))

from orpheus.tts import OrpheusTTS, Voice

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_timeout_fix():
    """Test that the timeout fix resolves the aiohttp error"""
    
    # Check if API key is available
    api_key = os.getenv("ORPHEUS_API_KEY")
    if not api_key:
        print("❌ ORPHEUS_API_KEY not set, cannot test")
        return
    
    print("🔧 Testing Timeout Fix")
    print("=" * 30)
    
    # Test with a simple short text
    test_text = "Hello, this is a timeout test."
    
    try:
        # Test streaming mode
        print("🔄 Testing streaming mode...")
        streaming_voice = Voice(id="tara", name="Tara")
        streaming_tts = OrpheusTTS(
            voice=streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = streaming_tts.synthesize(test_text)
        
        frame_count = 0
        async for audio_event in synthesis_stream:
            frame_count += 1
            if frame_count >= 2:  # Just test first couple frames
                break
        
        print(f"  ✅ Streaming mode: Generated {frame_count} frames")
        await streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ Streaming mode failed: {e}")
        if "Timeout context manager" in str(e):
            print("  🚨 TIMEOUT ERROR STILL EXISTS!")
        return False
    
    try:
        # Test non-streaming mode
        print("📦 Testing non-streaming mode...")
        non_streaming_voice = Voice(id="tara_async", name="Tara")
        non_streaming_tts = OrpheusTTS(
            voice=non_streaming_voice,
            chunk_size_bytes=4096,
            api_key=api_key
        )
        
        synthesis_stream = non_streaming_tts.synthesize(test_text)
        
        frame_count = 0
        async for audio_event in synthesis_stream:
            frame_count += 1
            if frame_count >= 2:  # Just test first couple frames
                break
        
        print(f"  ✅ Non-streaming mode: Generated {frame_count} frames")
        await non_streaming_tts.aclose()
        
    except Exception as e:
        print(f"  ❌ Non-streaming mode failed: {e}")
        if "Timeout context manager" in str(e):
            print("  🚨 TIMEOUT ERROR STILL EXISTS!")
        return False
    
    print()
    print("✅ Timeout fix successful!")
    print("🎯 Both streaming and non-streaming modes work without timeout errors")
    return True

async def main():
    """Run the test"""
    success = await test_timeout_fix()
    
    if success:
        print()
        print("🎉 Ready to test audio artifacts!")
        print("   - Use 'tara' for streaming mode")
        print("   - Use 'tara_async' for non-streaming mode")
        print("   - Compare audio quality between the two")
    else:
        print()
        print("❌ Fix needed - timeout errors still occurring")

if __name__ == "__main__":
    asyncio.run(main())
