import sys
import os
# Ensure the current working directory is in sys.path
# This helps locate local modules like 'orpheus' when the script is run from a subdirectory
# but its CWD is set to the project root (e.g., Orp_tts_call_telnyx).
if os.getcwd() not in sys.path:
    sys.path.insert(0, os.getcwd())

import asyncio
import json
import logging
from dotenv import load_dotenv
import os
from time import perf_counter
from livekit import rtc, api
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    llm
)
from livekit.protocol.agent import JobType
from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins import deepgram, openai, silero, cartesia , rime
import orpheus
from groq_stt import GroqSTT

# Set environment variables for agent configuration
import os
os.environ['STT_PROVIDER'] = 'deepgram'
os.environ['VOICE_ID'] = 'tara_async'
os.environ['MODEL_ID'] = 'llama-3.3-70b-versatile'
os.environ['CHUNK_SIZE_BYTES'] = '4096'
os.environ['STREAMING_MODE'] = 'auto'


# Load environment variables (optional, for local development)
load_dotenv(dotenv_path=".env.local")
# 🔧 FIX: Use dynamic agent name for logger instead of hardcoded "outbound-caller"
logger = logging.getLogger("21312")
logger.setLevel(logging.INFO)

outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
user_identity = "21312"  # This will be replaced by the backend

# Updated system prompt for Velina
_system_prompt = """your name is cb """

async def trim_context(assistant, chat_ctx: llm.ChatContext):
    if len(chat_ctx.messages) > 10:
        if chat_ctx.messages[0].role == "system":
            system_msg = chat_ctx.messages[0]
            non_system = chat_ctx.messages[1:]
            trimmed_non_system = non_system[-9:]
            chat_ctx.messages = [system_msg] + trimmed_non_system
        else:
            chat_ctx.messages = chat_ctx.messages[-10:]
        logger.info("Chat context trimmed (system message preserved if present).")

async def entrypoint(ctx: JobContext):
    global _system_prompt, user_identity
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info(f"Job details: job_id='{ctx.job.id}', metadata='{ctx.job.metadata}', data='{getattr(ctx.job, 'data', 'N/A')}'")
    logger.info(f"Full ctx.job object: {ctx.job}")
    metadata = ctx.job.metadata
    logger.info(f"Web calling session for {metadata} in room {ctx.room.name}")

    instructions = _system_prompt

    # Wait for any participant to join (web user)
    logger.info("Waiting for web participant to join...")
    participant = await ctx.wait_for_participant()
    logger.info(f"Web participant joined: {participant.identity}")
    
    await run_voice_pipeline_agent(ctx, participant, instructions)

    # Keep the session alive as long as participant is connected
    logger.info("Keeping session alive, waiting for participant...")
    try:
        while True:
            # Check if participant is still connected
            if hasattr(participant, 'disconnect_reason') and participant.disconnect_reason is not None:
                logger.info(f"Participant disconnected: {participant.disconnect_reason}")
                break
            
            # Check if participant is still in the room
            participants = ctx.room.remote_participants
            if participant.identity not in [p.identity for p in participants.values()]:
                logger.info("Participant no longer in room")
                break
                
            await asyncio.sleep(1)
    except Exception as e:
        logger.error(f"Error in session loop: {e}")

    logger.info("Web calling session ended")
    ctx.shutdown()

class CallActions(llm.FunctionContext):
    def __init__(self, *, api: api.LiveKitAPI, participant: rtc.RemoteParticipant, room: rtc.Room, tts, agent=None):
        super().__init__()
        self.api = api
        self.participant = participant
        self.room = room
        self.tts = tts
        self.agent = agent

    async def hangup(self):
        try:
            await self.api.room.remove_participant(
                api.RoomParticipantIdentity(
                    room=self.room.name,
                    identity=self.participant.identity,
                )
            )
        except Exception as e:
            logger.info(f"Received error while ending call: {e}")

    # REMOVED: end_call function - agents should NOT be able to automatically end calls

async def run_voice_pipeline_agent(
    ctx: JobContext, participant: rtc.RemoteParticipant, instructions: str
):
    logger.info("Starting voice pipeline agent")

    initial_ctx = llm.ChatContext().append(
        role="system",
        text=instructions,
    )

    call_actions = CallActions(
        api=ctx.api,
        participant=participant,
        room=ctx.room,
        tts=ctx.proc.userdata["tts"]
    )
    
    agent = VoicePipelineAgent(
        vad=ctx.proc.userdata["vad"],
        stt=ctx.proc.userdata["stt"],
        llm=ctx.proc.userdata["llm"],
        tts=ctx.proc.userdata["tts"],
        chat_ctx=initial_ctx,
        fnc_ctx=call_actions,
        before_llm_cb=trim_context,
        # REMOVED: min_endpointing_delay and max_endpointing_delay to prevent audio cutoffs
        allow_interruptions=True,
        interrupt_speech_duration=2,
        interrupt_min_words=2,
        preemptive_synthesis=True,
    )
    
    # Set agent reference in call_actions
    call_actions.agent = agent

    logger.info("Starting agent with participant")
    agent.start(ctx.room, participant)
    
    # Wait a moment for the agent to be ready
    await asyncio.sleep(1)
    
    # Initial greeting for web call
    logger.info("Sending initial greeting")
    await agent.say("Hello! How can I help you today?", allow_interruptions=True)
    
    logger.info("Voice pipeline agent setup complete")

def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()
    
    # 🔧 FIX: Get STT provider from environment variable with better debugging
    stt_provider = os.getenv("STT_PROVIDER", "groq").lower()
    logger.info(f"🎙️ Configuring STT provider: {stt_provider}")
    
    if stt_provider == "groq":
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            logger.error("❌ GROQ_API_KEY not found in environment!")
            raise ValueError("GROQ_API_KEY is required for Groq STT")
        
        proc.userdata["stt"] = GroqSTT(
            model="whisper-large-v3-turbo",
            api_key=groq_api_key
        )
        logger.info(f"✅ STT configured: Groq Whisper (whisper-large-v3-turbo)")
        
    elif stt_provider == "deepgram":
        deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
        if not deepgram_api_key:
            logger.error("❌ DEEPGRAM_API_KEY not found in environment!")
            raise ValueError("DEEPGRAM_API_KEY is required for Deepgram STT")
            
        proc.userdata["stt"] = deepgram.STT(
            model="nova-2-conversationalai",
            interim_results=True,
            api_key=deepgram_api_key
        )
        logger.info(f"✅ STT configured: Deepgram (nova-2-conversationalai)")
        
    else:
        # Default to groq with warning
        logger.warning(f"⚠️ Unknown STT provider '{stt_provider}', defaulting to Groq")
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            logger.error("❌ GROQ_API_KEY not found in environment!")
            raise ValueError("GROQ_API_KEY is required for Groq STT")
            
        proc.userdata["stt"] = GroqSTT(
            model="whisper-large-v3-turbo",
            api_key=groq_api_key
        )
        logger.info(f"✅ STT configured: Groq Whisper (default fallback)")
    from livekit.plugins.openai import llm
    
    # proc.userdata["llm"] = openai.LLM(
    #     model="gpt-4o-mini",
    #     api_key=os.getenv("OPENAI_API_KEY")
    # )
    proc.userdata["llm"] = llm.LLM.with_groq(
         model="llama-3.3-70b-versatile",
        #  temperature=0.8,
    )   

    # proc.userdata["tts"] = rime.TTS(
    #         api_key=os.getenv("RIME_API"),
    #         model="mistv2",
    #         speaker="Tanya",
    #         reduce_latency=True,
    # )
    # proc.userdata["tts"] = elevenlabs.TTS()
    # # proc.userdata["tts"] = deepgram.TTS(
    # #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    # #     model="aura-athena-en"
    # # )
    
    # Setup TTS with voice selection - can be extended to get from environment/agent config
    voice_id = os.getenv("VOICE_ID", "tara").lower()  # Get from environment or default to Tara
    
    # Get chunk size from environment variable (set by backend from frontend config)
    chunk_size_bytes = int(os.getenv("CHUNK_SIZE_BYTES", "4096"))
    
    # FIXED: Create voice object - handle both streaming and non-streaming voice IDs
    from orpheus.tts import Voice
    if voice_id in ["tara", "tara_async"]:
        voice = Voice(id=voice_id, name="Tara")  # Use actual voice_id (with _async if specified)
    elif voice_id in ["elise", "elise_async"]:
        voice = Voice(id=voice_id, name="Elise")  # Use actual voice_id (with _async if specified)
    else:
        voice = Voice(id="tara", name="Tara")  # Default to Tara streaming
    
    print(f"🎤 Initializing TTS with voice: {voice.name} ({voice.id})")
    print(f"🎯 Using frontend chunk size: {chunk_size_bytes} bytes")
    proc.userdata["tts"] = orpheus.OrpheusTTS(
        voice=voice, 
        chunk_size_bytes=chunk_size_bytes
    )
    # proc.userdata["tts"] = deepgram.TTS(
    #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    #     model="aura-athena-en"
    # )
    # proc.userdata["tts"] =cartesia.TTS(
    #     model= "sonic-2", 
    #     # voice="bf0a246a-8642-498a-9950-80c35e9276b5"
    # )

if __name__ == "__main__":
    opts = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm, agent_name="21312")
    cli.run_app(opts)
 