/**
 * Audio optimization utilities to prevent click sounds and audio artifacts
 * during initialization and playback in web applications.
 */

// Global audio context management
let globalAudioContext: AudioContext | null = null;

/**
 * Initialize the global audio context smoothly to prevent click sounds
 */
export const initializeAudioContext = async (): Promise<AudioContext> => {
  if (globalAudioContext && globalAudioContext.state !== 'closed') {
    return globalAudioContext;
  }

  try {
    // Create audio context with LiveKit-compatible settings
    globalAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      latencyHint: 'interactive',
      sampleRate: 24000  // FIXED: Match LiveKit's 24000 Hz to prevent resampling artifacts
    });

    // Ensure context is running
    if (globalAudioContext.state === 'suspended') {
      await globalAudioContext.resume();
    }

    // Create a brief silent buffer to "warm up" the audio system
    const buffer = globalAudioContext.createBuffer(1, 1, globalAudioContext.sampleRate);
    const source = globalAudioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(globalAudioContext.destination);
    source.start();

    console.log('🎵 Audio context initialized smoothly');
    return globalAudioContext;
  } catch (error) {
    console.error('Failed to initialize audio context:', error);
    throw error;
  }
};

// Volume ramping function removed per user request - no longer manipulating audio volume

/**
 * Smooth audio element initialization to prevent click sounds
 */
export const initializeAudioElementSmoothly = async (audioElement: HTMLAudioElement): Promise<() => void> => {
  // Ensure audio context is initialized
  await initializeAudioContext();
  
  // Set initial properties
  audioElement.volume = 0;
  audioElement.preload = 'none';
  
  // Add event listeners for basic playback
  const handlePlay = () => {
    console.log('Audio started playing');
  };
  
  const handlePause = () => {
    // Smooth fade out when pausing
    const currentVolume = audioElement.volume;
    const fadeOut = (volume: number) => {
      if (volume > 0 && !audioElement.paused) {
        audioElement.volume = Math.max(0, volume - 0.1);
        setTimeout(() => fadeOut(audioElement.volume), 10);
      }
    };
    fadeOut(currentVolume);
  };
  
  audioElement.addEventListener('play', handlePlay);
  audioElement.addEventListener('pause', handlePause);
  
  // Return cleanup function
  return () => {
    audioElement.removeEventListener('play', handlePlay);
    audioElement.removeEventListener('pause', handlePause);
  };
};

/**
 * Optimize microphone activation to prevent click sounds
 */
export const optimizeMicrophoneActivation = async (micActivationFunction: () => Promise<void>): Promise<void> => {
  try {
    // Initialize audio context first
    await initializeAudioContext();
    
    // Brief delay to allow audio system to stabilize
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // Activate microphone
    await micActivationFunction();
    
    // Additional delay for microphone to fully initialize
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('🎤 Microphone activated with click prevention');
  } catch (error) {
    console.error('Error during optimized microphone activation:', error);
    throw error;
  }
};

/**
 * Preload and optimize audio for smooth playback
 */
export const preloadAudioSmoothly = async (audioUrl: string): Promise<HTMLAudioElement> => {
  const audio = new Audio();
  
  // Initialize smoothly
  await initializeAudioElementSmoothly(audio);
  
  return new Promise((resolve, reject) => {
    const handleCanPlay = () => {
      audio.removeEventListener('canplaythrough', handleCanPlay);
      audio.removeEventListener('error', handleError);
      resolve(audio);
    };
    
    const handleError = (e: ErrorEvent) => {
      audio.removeEventListener('canplaythrough', handleCanPlay);
      audio.removeEventListener('error', handleError);
      reject(e);
    };
    
    audio.addEventListener('canplaythrough', handleCanPlay);
    audio.addEventListener('error', handleError);
    
    audio.src = audioUrl;
    audio.load();
  });
};

export default {
  initializeAudioContext,
  initializeAudioElementSmoothly,
  optimizeMicrophoneActivation,
  preloadAudioSmoothly
}; 