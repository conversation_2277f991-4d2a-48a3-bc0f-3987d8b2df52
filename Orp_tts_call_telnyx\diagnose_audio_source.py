#!/usr/bin/env python3
"""
Diagnostic script to test the raw audio data from the Baseten API.
This will help determine if the artifacts are in the source audio or in our processing.
"""

import asyncio
import aiohttp
import logging
import os
import struct
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_raw_api_audio():
    """Test the raw audio from the Baseten API directly"""
    
    api_key = os.getenv("ORPHEUS_API_KEY")
    if not api_key:
        print("❌ ORPHEUS_API_KEY not set, cannot test")
        return
    
    api_url = "https://model-5qenjjpq.api.baseten.co/environments/production/predict"
    test_text = "Hello, this is a test to check the raw audio quality from the API."
    
    print("🔍 Diagnosing Raw Audio from Baseten API")
    print("=" * 50)
    print(f"API URL: {api_url}")
    print(f"Test text: '{test_text}'")
    print()
    
    # Test both streaming and non-streaming
    for mode, payload in [
        ("NON-STREAMING", {
            'model_input': {
                'voice': 'tara',
                'prompt': test_text,
                'max_tokens': 10000
            }
        }),
        ("STREAMING", {
            'voice': 'tara',
            'prompt': test_text,
            'max_tokens': 10000
        })
    ]:
        print(f"🧪 Testing {mode} mode...")
        
        try:
            headers = {
                "Authorization": f"Api-Key {api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(api_url, headers=headers, json=payload) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        print(f"  ❌ API error: {resp.status} - {error_text}")
                        continue
                    
                    # Read the complete response
                    audio_data = await resp.read()
                    print(f"  📊 Received {len(audio_data)} bytes")
                    
                    # Analyze the audio data
                    await analyze_audio_data(audio_data, mode)
                    
                    # Save to file for manual inspection
                    filename = f"raw_audio_{mode.lower()}.wav"
                    with open(filename, "wb") as f:
                        f.write(audio_data)
                    print(f"  💾 Saved to {filename}")
                    
        except Exception as e:
            print(f"  ❌ Error testing {mode}: {e}")
        
        print()

async def analyze_audio_data(audio_data: bytes, mode: str):
    """Analyze the raw audio data for potential issues"""
    
    print(f"  🔍 Analyzing {mode} audio data...")
    
    # Check if it's a valid WAV file
    if audio_data.startswith(b'RIFF') and b'WAVE' in audio_data[:12]:
        print("  ✅ Valid WAV file detected")
        
        # Parse WAV header
        try:
            # WAV header structure: RIFF(4) + size(4) + WAVE(4) + fmt (4) + fmt_size(4) + format_data(16+)
            if len(audio_data) >= 44:
                fmt_chunk = audio_data[20:36]
                audio_format, channels, sample_rate, byte_rate, block_align, bits_per_sample = struct.unpack('<HHIIHH', fmt_chunk)
                
                print(f"    Format: {audio_format} (1=PCM)")
                print(f"    Channels: {channels}")
                print(f"    Sample Rate: {sample_rate} Hz")
                print(f"    Bits per Sample: {bits_per_sample}")
                print(f"    Byte Rate: {byte_rate}")
                print(f"    Block Align: {block_align}")
                
                # Check for expected values
                if sample_rate != 24000:
                    print(f"  ⚠️  WARNING: Sample rate is {sample_rate}, expected 24000")
                if channels != 1:
                    print(f"  ⚠️  WARNING: Channels is {channels}, expected 1")
                if bits_per_sample != 16:
                    print(f"  ⚠️  WARNING: Bits per sample is {bits_per_sample}, expected 16")
                
                # Calculate audio duration
                data_size = len(audio_data) - 44  # Subtract WAV header
                duration = data_size / (sample_rate * channels * (bits_per_sample // 8))
                print(f"    Duration: {duration:.2f} seconds")
                
                # Check for audio data patterns that might cause artifacts
                audio_samples = audio_data[44:]  # Skip WAV header
                await check_audio_patterns(audio_samples, sample_rate, mode)
                
        except Exception as e:
            print(f"  ❌ Error parsing WAV header: {e}")
    else:
        print("  ❌ Not a valid WAV file")
        print(f"    First 16 bytes: {audio_data[:16]}")

async def check_audio_patterns(audio_samples: bytes, sample_rate: int, mode: str):
    """Check for patterns in audio data that might cause artifacts"""
    
    print(f"  🎵 Checking audio patterns...")
    
    # Convert to 16-bit samples
    if len(audio_samples) % 2 != 0:
        print("  ⚠️  WARNING: Odd number of bytes in audio data")
        audio_samples = audio_samples[:-1]
    
    # Parse as 16-bit signed integers
    samples = []
    for i in range(0, len(audio_samples), 2):
        if i + 1 < len(audio_samples):
            sample = struct.unpack('<h', audio_samples[i:i+2])[0]
            samples.append(sample)
    
    if not samples:
        print("  ❌ No audio samples found")
        return
    
    print(f"    Total samples: {len(samples)}")
    print(f"    Sample range: {min(samples)} to {max(samples)}")
    
    # Check for silence at start/end (might indicate padding issues)
    start_silence = 0
    for sample in samples[:1000]:  # Check first 1000 samples
        if abs(sample) < 100:  # Very quiet
            start_silence += 1
        else:
            break
    
    end_silence = 0
    for sample in reversed(samples[-1000:]):  # Check last 1000 samples
        if abs(sample) < 100:  # Very quiet
            end_silence += 1
        else:
            break
    
    print(f"    Start silence: {start_silence} samples ({start_silence/sample_rate*1000:.1f}ms)")
    print(f"    End silence: {end_silence} samples ({end_silence/sample_rate*1000:.1f}ms)")
    
    # Check for sudden jumps (might cause clicks/pops)
    large_jumps = 0
    for i in range(1, min(len(samples), 10000)):  # Check first 10k samples
        diff = abs(samples[i] - samples[i-1])
        if diff > 5000:  # Large jump
            large_jumps += 1
    
    if large_jumps > 0:
        print(f"  ⚠️  WARNING: Found {large_jumps} large amplitude jumps (potential clicks/pops)")
    else:
        print("  ✅ No large amplitude jumps detected")
    
    # Check for clipping
    clipped_samples = sum(1 for s in samples if abs(s) >= 32767)
    if clipped_samples > 0:
        print(f"  ⚠️  WARNING: Found {clipped_samples} clipped samples")
    else:
        print("  ✅ No clipping detected")

async def main():
    """Run the diagnostic"""
    await test_raw_api_audio()
    
    print("🎯 Diagnostic Summary:")
    print("=" * 30)
    print("1. Check the saved WAV files manually with audio software")
    print("2. If artifacts exist in the raw files, the issue is with the API")
    print("3. If raw files are clean, the issue is in our processing pipeline")
    print("4. Pay attention to sample rate, silence padding, and amplitude jumps")

if __name__ == "__main__":
    asyncio.run(main())
