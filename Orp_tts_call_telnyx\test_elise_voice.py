#!/usr/bin/env python3
"""
Test script specifically for <PERSON> voice to debug the "Executor shutdown" error.
"""

import os
import sys
import logging
import asyncio

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from orpheus.tts import OrpheusTTS, Voice

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_elise_voice():
    """Test Elise voice specifically"""
    logger.info("🧪 Testing Elise voice...")
    
    try:
        # Create TTS instance with <PERSON> voice
        voice = Voice(id="elise", name="<PERSON>")
        tts = OrpheusTTS(voice=voice, chunk_size_bytes=1024)
        
        # Test text
        test_text = "Hello! This is a test of the Elise voice."
        
        logger.info(f"Testing Elise voice with text: '{test_text}'")
        logger.info(f"Voice config: {tts._voice_config}")
        logger.info(f"API URL: {tts._opts.api_url}")
        
        # Create synthesis stream
        stream = tts.synthesize(test_text)
        
        logger.info("✅ Elise voice TTS stream created successfully")
        
        # Close the stream
        await stream.aclose()
        
        logger.info("✅ Elise voice test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Elise voice test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tara_voice():
    """Test Tara voice for comparison"""
    logger.info("🧪 Testing Tara voice for comparison...")
    
    try:
        # Create TTS instance with Tara voice
        voice = Voice(id="tara", name="Tara")
        tts = OrpheusTTS(voice=voice, chunk_size_bytes=1024)
        
        # Test text
        test_text = "Hello! This is a test of the Tara voice."
        
        logger.info(f"Testing Tara voice with text: '{test_text}'")
        logger.info(f"Voice config: {tts._voice_config}")
        logger.info(f"API URL: {tts._opts.api_url}")
        
        # Create synthesis stream
        stream = tts.synthesize(test_text)
        
        logger.info("✅ Tara voice TTS stream created successfully")
        
        # Close the stream
        await stream.aclose()
        
        logger.info("✅ Tara voice test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tara voice test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_management():
    """Test session management specifically"""
    logger.info("🧪 Testing session management...")
    
    try:
        # Create TTS instance
        voice = Voice(id="elise", name="Elise")
        tts = OrpheusTTS(voice=voice)
        
        # Test session creation
        session1 = await tts._ensure_session()
        logger.info(f"Session 1 created: {session1}")
        logger.info(f"Session 1 closed: {session1.closed}")
        
        # Test session reuse
        session2 = await tts._ensure_session()
        logger.info(f"Session 2 created: {session2}")
        logger.info(f"Session 2 closed: {session2.closed}")
        logger.info(f"Same session: {session1 is session2}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Session management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_configs():
    """Test voice configurations"""
    logger.info("🧪 Testing voice configurations...")
    
    try:
        from orpheus.tts import VOICE_CONFIGS
        
        logger.info("Available voice configurations:")
        for voice_id, config in VOICE_CONFIGS.items():
            logger.info(f"  {voice_id}: {config}")
        
        # Test Elise config specifically
        elise_config = VOICE_CONFIGS.get("elise")
        if elise_config:
            logger.info(f"✅ Elise config found: {elise_config}")
            logger.info(f"  API URL: {elise_config['api_url']}")
            logger.info(f"  Streaming: {elise_config['is_streaming']}")
            logger.info(f"  Voice name: {elise_config['api_voice_name']}")
        else:
            logger.error("❌ Elise config not found")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Voice config test failed: {e}")
        return False

async def main():
    """Run all Elise voice tests"""
    logger.info("🚀 Starting Elise voice debugging tests...")
    
    tests = [
        ("Voice Configurations", test_voice_configs),
        ("Session Management", test_session_management),
        ("Tara Voice (Control)", test_tara_voice),
        ("Elise Voice (Problem)", test_elise_voice),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("ELISE VOICE TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Elise voice tests passed!")
        logger.info("The 'Executor shutdown' error should be fixed.")
    else:
        logger.error("⚠️ Some tests failed. The Elise voice issue may persist.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
