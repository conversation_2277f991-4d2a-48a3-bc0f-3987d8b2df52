# Audio Artifacts Solution - "Tuk Tuk" Sound Fix

## Problem Analysis

You're experiencing "tuk tuk" sound artifacts and audio cutting issues in your QuickCall application. The key insight is that **if non-streaming mode still has artifacts, the issue is NOT in the chunking/streaming logic**.

## Root Cause Investigation

Since non-streaming mode should receive complete audio files without any chunking, but still has artifacts, the problem is likely in one of these areas:

### 1. **Source Audio Quality** (Most Likely)
- The Baseten API itself might be returning audio with artifacts
- Audio generation quality issues at the source
- Encoding/compression artifacts from the TTS model

### 2. **Audio Frame Creation**
- How LiveKit AudioFrame objects are created from raw audio data
- Sample rate or format conversion issues
- Memory alignment or byte order problems

### 3. **LiveKit/Browser Pipeline**
- LiveKit's audio processing pipeline
- Browser audio decoding/playback
- WebRTC audio handling

## Fixes Applied

### ✅ Streaming vs Non-Streaming Separation
**File**: `orpheus/tts.py`

```python
# CRITICAL FIX: Different processing for streaming vs non-streaming
if self._is_streaming:
    # STREAMING MODE: Use chunked processing with AudioByteStream
    # ... chunked processing logic ...
else:
    # NON-STREAMING MODE: Read complete audio file and push directly
    complete_audio = await resp.read()
    
    # Remove WAV header if present
    if complete_audio.startswith(b'RIFF') and len(complete_audio) >= 44:
        complete_audio = complete_audio[44:]
    
    # Push complete audio directly - no chunking, no AudioByteStream
    if complete_audio:
        output_emitter.push(complete_audio)
```

### ✅ Improved Frame Processing
- Increased frame size from 10ms to 20ms (VoIP standard)
- Larger chunk sizes (4KB instead of 1KB) for smoother processing
- Better audio buffering with minimum buffer size
- Proper AudioByteStream usage for streaming mode only

### ✅ Optimized Audio Settings
- Reduced silence padding from 50ms to 20ms
- Optimized text thresholds for better responsiveness
- Consistent 24kHz sample rate throughout pipeline

## Diagnostic Tools

### 1. **Test Non-Streaming Mode**
```bash
cd Orp_tts_call_telnyx
python test_non_streaming.py
```

### 2. **Diagnose Raw Audio Quality**
```bash
cd Orp_tts_call_telnyx
python diagnose_audio_source.py
```

This will:
- Test both streaming and non-streaming API calls
- Save raw audio files for manual inspection
- Analyze audio data for potential issues
- Check for clipping, jumps, and format problems

### 3. **Test Audio Processing Fixes**
```bash
cd Orp_tts_call_telnyx
python test_audio_fixes.py
```

## Next Steps

### 1. **Immediate Testing**
1. Run the diagnostic script to check raw audio quality
2. Test non-streaming mode specifically (use `tara_async` voice)
3. Compare artifacts between streaming and non-streaming

### 2. **If Raw Audio Has Artifacts**
The issue is with the Baseten API or TTS model:
- Contact Baseten support about audio quality
- Try different voice models
- Adjust TTS parameters (speed, pitch, etc.)
- Consider alternative TTS providers

### 3. **If Raw Audio Is Clean**
The issue is in our processing pipeline:
- Check LiveKit AudioFrame creation
- Investigate browser audio handling
- Test with different audio formats
- Check WebRTC audio settings

### 4. **Browser-Specific Issues**
If artifacts only occur in web calling:
- Test with different browsers
- Check browser audio settings
- Investigate WebRTC audio processing
- Consider audio codec issues

## Voice Configuration

### Streaming Voices
- `tara` - Streaming mode with chunked processing
- `elise` - Streaming mode with chunked processing

### Non-Streaming Voices  
- `tara_async` - Non-streaming mode with complete audio files
- `elise_async` - Non-streaming mode with complete audio files

## Expected Results

### Non-Streaming Mode Should:
- ✅ Receive complete audio files
- ✅ Have NO chunking artifacts
- ✅ Process audio as single frames
- ✅ Eliminate "tuk tuk" sounds from frame boundaries

### If Non-Streaming Still Has Artifacts:
- 🔍 Issue is in source audio or LiveKit/browser pipeline
- 🔍 NOT related to our chunking/streaming logic
- 🔍 Focus investigation on API quality and audio frame handling

## Testing Commands

```bash
# Test with non-streaming voice
export VOICE_ID=tara_async
python your_agent.py

# Test with streaming voice  
export VOICE_ID=tara
python your_agent.py

# Compare the audio quality between the two modes
```

## Key Insight

**The fact that non-streaming mode still has artifacts is the crucial diagnostic information.** This tells us the problem is NOT in our streaming/chunking logic, but either:

1. **Source audio quality** from the Baseten API
2. **LiveKit audio frame processing** 
3. **Browser/WebRTC audio handling**

Focus your investigation on these areas rather than the streaming pipeline.
